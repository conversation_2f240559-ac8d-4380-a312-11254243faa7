{"tasks": [{"type": "cppbuild", "label": "C/C++: gcc.exe build active file", "command": "D:/CODING_AND_PROGRAMMING/IDES_EDITORS_AND_SHELLS/OS-DEV/CYGWIN/INTERNAL/bin/gcc.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe", ""], "options": {"cwd": "D:/CODING_AND_PROGRAMMING/IDES_EDITORS_AND_SHELLS/OS-DEV/CYGWIN/INTERNAL/bin"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Task generated by <PERSON>bugger."}], "version": "2.0.0"}