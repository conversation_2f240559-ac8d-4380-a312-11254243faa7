@echo off
echo ========================================
echo BYPASS ISOLATION TEST
echo ========================================
echo.
echo This test bypasses ALL detection and forces known values
echo at each step to isolate exactly where the corruption occurs.
echo.

echo Testing with bypass detection (forces known values)
echo This will show exactly where 4294948864 KB appears
echo.
timeout /t 2 > nul
start /wait qemu-system-i386 -hda os.img -m 16384 -display none -serial stdio -no-reboot
echo.

echo ========================================
echo BYPASS ANALYSIS
echo ========================================
echo.
echo Look for these patterns in the output:
echo.
echo BYPASS 1 - FORCE 12345678: 12345678 KB    (Should show 12345678)
echo BYPASS 2 - CHECK PERSIST: 12345678 KB     (Should still show 12345678)
echo BYPASS 3 - FORCE 16777216: 16777216 KB    (Should show 16777216)
echo BYPASS 4 - BEFORE ESTIMATE: 16777216 KB   (Should show 16777216)
echo BYPASS 4 - AFTER ESTIMATE: [value] KB     (Check if estimate corrupts)
echo BYPASS 5 - FORCE AGAIN: 16777216 KB       (Should show 16777216)
echo BYPASS 6 - ALT MEMORY: 87654321 KB        (Different memory location)
echo BYPASS 7 - RAM_TOTAL_KB ADDR: [address]   (Memory address)
echo BYPASS 8 - RAW BYTES: [bytes]             (Raw byte values)
echo.
echo CRITICAL ANALYSIS:
echo.
echo If BYPASS 1 shows 4294948864 instead of 12345678:
echo   → Memory variable is corrupted from the start
echo   → Problem is in memory layout or variable definition
echo.
echo If BYPASS 2 shows 4294948864 but BYPASS 1 was correct:
echo   → Memory is being corrupted between operations
echo   → Stack overflow or memory corruption issue
echo.
echo If BYPASS 4 AFTER shows 4294948864 but BEFORE was correct:
echo   → estimate_ram_specs function is corrupting memory
echo   → Problem is in the estimation function
echo.
echo If all BYPASS steps show correct values but final display shows 4294948864:
echo   → Problem is in userland data transfer or display logic
echo   → Corruption happens after kernel processing
echo.
echo If BYPASS 6 (alt memory) also shows corruption:
echo   → Widespread memory corruption (stack overflow, etc.)
echo   → System-wide memory issue
echo.
echo If BYPASS 8 shows unexpected byte pattern:
echo   → Memory address corruption or alignment issue
echo   → Hardware or emulation problem
echo.
pause
