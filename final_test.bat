@echo off
echo ========================================
echo FINAL RAM DETECTION FIX VERIFICATION
echo ========================================
echo.

echo Step 1: Binary Analysis Results
echo --------------------------------
powershell -ExecutionPolicy Bypass -File check_binary.ps1
echo.

echo Step 2: Testing OS Boot
echo -----------------------
echo Starting QEMU with 512MB RAM...
echo (This should show 524288 KB instead of 4294948864 KB)
echo.

echo Press Ctrl+C to stop QEMU when you see the RAM value displayed.
echo.

qemu-system-i386 -hda os.img -m 512 -serial stdio

echo.
echo ========================================
echo TEST COMPLETE
echo ========================================
echo.
echo Expected Result: RAM should show 524288 KB (512MB)
echo Old Buggy Result: RAM would show 4294948864 KB
echo.
echo If you saw 524288 KB, the fix is working!
echo If you saw 4294948864 KB, there's still an issue.
echo.
pause
