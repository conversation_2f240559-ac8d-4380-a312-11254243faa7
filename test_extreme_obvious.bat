@echo off
echo ========================================
echo EXTREME OBVIOUS VALUES TEST
echo ========================================
echo.
echo This test forces EXTREMELY OBVIOUS values that are
echo impossible to miss if our code changes are working:
echo.
echo - RAM: 99999999 KB (99GB) - impossible to miss
echo - Speed: 9999 MHz - impossible to miss
echo - Disk: 88888888 KB (88GB) - impossible to miss
echo - Chunks: 7777 KB, 6666 total - impossible to miss
echo.
echo If these obvious values don't appear, it means:
echo 1. Our kernel changes aren't being executed
echo 2. Our userland changes aren't being executed  
echo 3. There's a fundamental build/load issue
echo 4. The display is completely hardcoded elsewhere
echo.

echo Running extreme obvious values test...
timeout /t 2 > nul
start /wait qemu-system-i386 -hda os.img -m 16384 -display none -serial stdio -no-reboot
echo.

echo ========================================
echo EXTREME TEST ANALYSIS
echo ========================================
echo.
echo CRITICAL QUESTIONS:
echo.
echo 1. Did you see RAM: 99999999 KB (99GB)? (Y/N)
set /p ram_obvious=
echo.
echo 2. Did you see Speed: 9999 MHz? (Y/N)
set /p speed_obvious=
echo.
echo 3. Did you see Disk: 88888888 KB (88GB)? (Y/N)
set /p disk_obvious=
echo.
echo 4. Did you see Chunks: 7777 KB? (Y/N)
set /p chunk_obvious=
echo.

echo ========================================
echo DIAGNOSTIC RESULTS
echo ========================================
echo.
if "%ram_obvious%"=="Y" if "%speed_obvious%"=="Y" if "%disk_obvious%"=="Y" if "%chunk_obvious%"=="Y" (
    echo ✅ SUCCESS: OUR CODE CHANGES ARE WORKING!
    echo.
    echo This proves that:
    echo • Kernel changes ARE being executed
    echo • Userland changes ARE being executed
    echo • Build process is working correctly
    echo • Display system is reading our values
    echo.
    echo The 4294948864 KB issue is NOT due to:
    echo • Build problems
    echo • Code not executing
    echo • Hardcoded display values
    echo.
    echo The issue must be in the SPECIFIC detection logic
    echo that's producing the 0xFFFFFC00 value.
    echo.
    echo NEXT STEP: Restore detection logic and debug
    echo the specific arithmetic that creates 4294948864.
) else (
    echo ❌ CRITICAL: OUR CODE CHANGES ARE NOT WORKING!
    echo.
    echo This reveals a fundamental problem:
    echo.
    if "%ram_obvious%"=="N" (
        echo ⚠️  RAM values not changed - kernel or userland issue
    )
    if "%speed_obvious%"=="N" (
        echo ⚠️  Speed values not changed - kernel or userland issue
    )
    if "%disk_obvious%"=="N" (
        echo ⚠️  Disk values not changed - kernel or userland issue
    )
    if "%chunk_obvious%"=="N" (
        echo ⚠️  Chunk values not changed - kernel or userland issue
    )
    echo.
    echo POSSIBLE CAUSES:
    echo • Build system not updating the image correctly
    echo • Kernel not being loaded or executed
    echo • Userland not being loaded or executed
    echo • Display values hardcoded in a different location
    echo • Memory corruption preventing value storage
    echo • QEMU caching old image
    echo.
    echo IMMEDIATE ACTIONS:
    echo • Check if os.img file timestamp updated
    echo • Verify build process completed without errors
    echo • Try deleting os.img and rebuilding completely
    echo • Check if QEMU is using cached image
)
echo.
pause
