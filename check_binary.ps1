# PowerShell script to check for problematic values in binaries

Write-Host "Binary Analysis for RAM Detection Fix" -ForegroundColor Cyan
Write-Host "=" * 40

# Problematic value: 4294948864 (0xFFFFFC00)
# In little-endian bytes: 00 FC FF FF
$problematicBytes = @(0x00, 0xFC, 0xFF, 0xFF)

# Correct value: 524288 (0x80000)  
# In little-endian bytes: 00 00 08 00
$correctBytes = @(0x00, 0x00, 0x08, 0x00)

function Check-Binary($filename) {
    if (!(Test-Path $filename)) {
        Write-Host "❌ File $filename not found" -ForegroundColor Red
        return $false
    }
    
    Write-Host "Checking $filename..." -ForegroundColor Yellow
    
    $bytes = [System.IO.File]::ReadAllBytes($filename)
    
    # Check for problematic value
    $foundProblematic = $false
    for ($i = 0; $i -le ($bytes.Length - 4); $i++) {
        if ($bytes[$i] -eq 0x00 -and $bytes[$i+1] -eq 0xFC -and $bytes[$i+2] -eq 0xFF -and $bytes[$i+3] -eq 0xFF) {
            Write-Host "❌ FOUND problematic value at offset 0x$($i.ToString('X4'))" -ForegroundColor Red
            $foundProblematic = $true
        }
    }
    
    if (!$foundProblematic) {
        Write-Host "✅ No problematic values found" -ForegroundColor Green
    }
    
    # Check for correct value
    $foundCorrect = $false
    for ($i = 0; $i -le ($bytes.Length - 4); $i++) {
        if ($bytes[$i] -eq 0x00 -and $bytes[$i+1] -eq 0x00 -and $bytes[$i+2] -eq 0x08 -and $bytes[$i+3] -eq 0x00) {
            Write-Host "✅ Found correct value 524288 at offset 0x$($i.ToString('X4'))" -ForegroundColor Green
            $foundCorrect = $true
        }
    }
    
    if (!$foundCorrect) {
        Write-Host "⚠️  Correct value 524288 not found (might be set at runtime)" -ForegroundColor Yellow
    }
    
    return !$foundProblematic
}

$files = @("kernel.bin", "userland.bin", "boot.bin")
$allClean = $true

foreach ($file in $files) {
    $clean = Check-Binary $file
    $allClean = $allClean -and $clean
    Write-Host ""
}

if ($allClean) {
    Write-Host "🎉 All binaries are clean of problematic values!" -ForegroundColor Green
} else {
    Write-Host "❌ Some binaries still contain problematic values!" -ForegroundColor Red
}

exit $(if ($allClean) { 0 } else { 1 })
