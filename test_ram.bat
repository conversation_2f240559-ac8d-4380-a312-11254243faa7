@echo off
echo Testing RAM detection with different memory sizes...
echo.

echo Testing with 512MB RAM:
qemu-system-i386 -drive format=raw,file=os.img -m 512 -display none -serial stdio -no-reboot > test_512mb.txt 2>&1 &
timeout /t 3 > nul
taskkill /f /im qemu-system-i386.exe > nul 2>&1

echo Testing with 1GB RAM:
qemu-system-i386 -drive format=raw,file=os.img -m 1024 -display none -serial stdio -no-reboot > test_1gb.txt 2>&1 &
timeout /t 3 > nul
taskkill /f /im qemu-system-i386.exe > nul 2>&1

echo Testing with 2GB RAM:
qemu-system-i386 -drive format=raw,file=os.img -m 2048 -display none -serial stdio -no-reboot > test_2gb.txt 2>&1 &
timeout /t 3 > nul
taskkill /f /im qemu-system-i386.exe > nul 2>&1

echo.
echo Test completed. Check test_*.txt files for results.
echo Expected results:
echo - 512MB should show: 524288 KB
echo - 1GB should show: 1048576 KB  
echo - 2GB should show: 2097152 KB
echo.
echo If you see 4294948864 KB, the bug is still present.
echo If you see the expected values, the fix is working!
