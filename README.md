# Fractal OS - Advanced Dynamic Loading System

## Overview
Fractal OS is an advanced operating system that implements dynamic hardware detection, adaptive memory management, and fractal loading patterns. The system automatically detects your hardware configuration and optimizes its behavior accordingly.

## Key Features

### 🔧 Hardware Detection
- **RAM Detection**: Automatically detects total RAM capacity, type (DDR3/DDR4/DDR5), and speed
- **Disk Detection**: Identifies disk type (HDD/SSD/NVMe) and capacity
- **Dynamic Optimization**: Adjusts chunk sizes and loading patterns based on detected hardware

### 🌀 Fractal Loading System
- **Mode Cycling**: Cycles through 6 different processor modes (Real → Protected → Long → PAE → Hybrid → Ultimate)
- **Exponential Spawning**: Creates new loading cycles in fractal patterns (1→11→121→1331...)
- **Dynamic Chunk Sizing**: Automatically calculates optimal chunk sizes based on available RAM
- **Real-time Cycle Calculation**: Shows exactly how many cycles are needed to load entire storage

### 💾 Memory Management
- **RAM-Aware Loading**: Adjusts loading amounts based on available RAM (128MB to 8GB chunks)
- **Thermal Management**: Implements cooling periods and thermal-aware loading
- **Safe Memory Allocation**: Prevents system crashes due to memory overload

### 🖥️ User Interface
- **Separated Userland**: Completely isolated userland that continues running even if it encounters errors
- **Real-time Updates**: Dynamic screen updates showing current system status
- **Light Cyan Interface**: Beautiful cyan background with real-time system information
- **Comprehensive Display**: Shows RAM type/speed, disk type/capacity, cycles needed, and progress

### 🛡️ Error Handling
- **Complete Isolation**: Userland failures don't affect kernel operation
- **Graceful Degradation**: System continues operating even if components fail
- **Safe Recovery**: Automatic error recovery and continuation

## System Requirements
- x86-64 compatible processor
- Minimum 512MB RAM (optimized for 1GB+)
- Any storage device (HDD/SSD/NVMe)
- BIOS or UEFI with legacy boot support

## Building the System

### Using Makefile (Recommended)
```bash
make clean
make
```

### Manual Build
```bash
nasm -f bin -Ox boot.asm -o boot.bin
nasm -f bin -Ox kernel.asm -o kernel.bin
nasm -f bin -Ox main.asm -o userland.bin
dd if=/dev/zero of=os.img bs=512 count=4096
dd if=boot.bin of=os.img conv=notrunc
dd if=kernel.bin of=os.img bs=512 seek=2 conv=notrunc
dd if=userland.bin of=os.img bs=512 seek=66 conv=notrunc
```

## Testing

### QEMU (Recommended)
```bash
make run
# or manually:
qemu-system-i386 -drive format=raw,file=os.img -m 1024
```

### VirtualBox
1. Create new VM with "Other/Unknown" OS type
2. Attach os.img as a floppy disk or hard drive
3. Boot from the attached image

### Real Hardware
Write os.img to a USB drive or floppy disk and boot from it.

## Architecture

### Boot Process
1. **Bootloader** (boot.asm): Loads kernel and userland, enables A20 line
2. **Kernel** (kernel.asm): Hardware detection, fractal loading engine
3. **Userland** (main.asm): User interface with complete error isolation

### Memory Layout
- `0x7C00`: Bootloader
- `0x1000:0x0000`: Kernel segment
- `0x2000:0x0000`: Userland segment
- `0x70000+`: Page tables (for long mode)

### Hardware Detection Process
1. **RAM Detection**: Uses INT 15h E820h to map memory, detects type based on capacity
2. **Disk Detection**: Uses INT 13h AH=48h for drive parameters
3. **Type Classification**: Analyzes capacity and characteristics
4. **Optimization**: Calculates optimal chunk sizes and loading patterns

### Fractal Loading Modes
1. **Real Mode**: Basic 16-bit loading with thermal management
2. **Protected Mode**: 32-bit loading with enhanced throughput
3. **Long Mode**: 64-bit loading with maximum performance
4. **PAE Mode**: Physical Address Extension for large memory
5. **Hybrid Mode**: Mixed addressing for optimal performance
6. **Ultimate Mode**: Maximum performance before spawning

## Dynamic Behavior

### Chunk Size Calculation
- **< 1GB RAM**: 128MB chunks
- **1-4GB RAM**: 512MB chunks  
- **4-16GB RAM**: 1GB chunks
- **> 16GB RAM**: 2GB chunks
- **Disk Type Multipliers**: HDD (0.5x), SSD (2x), NVMe (4x)

### Cycle Calculation
The system now dynamically calculates exactly how many cycles are needed:
- **Total Storage** is detected and converted to MB
- **Cycles Needed** = Total Storage MB / Chunk Size MB
- **Real-time Display** shows progress and remaining cycles

### Spawning Patterns
- **Small RAM**: Conservative spawning (1-2 cycles)
- **Large RAM**: Aggressive spawning (up to 10 cycles)
- **Thermal Limits**: Automatic restart at capacity limits

### Screen Updates
- **Real-time Status**: Shows current RAM type/speed, disk type/capacity
- **Progress Tracking**: Displays MB loaded and active cycles
- **Hardware Info**: Shows detected hardware specifications
- **Cycle Information**: Shows total cycles needed and current progress

## Enhanced Features (Latest Update)

### Improved Hardware Display
- **RAM Type Detection**: Shows DDR3/DDR4/DDR5 based on capacity
- **RAM Speed Display**: Shows detected or estimated RAM speed in MHz
- **Disk Type Classification**: HDD/SSD/NVMe based on capacity and characteristics
- **Total Storage Display**: Shows total storage capacity in MB

### Real-time Calculations
- **Dynamic Chunk Sizing**: Calculates optimal chunk sizes based on RAM and disk type
- **Cycle Prediction**: Calculates and displays total cycles needed
- **Progress Tracking**: Real-time progress updates during loading

### Enhanced Userland
- **Comprehensive Display**: Shows all system information in real-time
- **Error Isolation**: Complete separation from kernel operations
- **Dynamic Updates**: Receives real-time data from kernel

## Safety Features

### Error Isolation
- Userland runs in complete isolation
- Kernel continues even if userland fails
- Automatic error recovery and continuation

### Thermal Management
- Cooling periods between intensive operations
- Dynamic load adjustment based on hardware
- Automatic restart when thermal limits reached

### Memory Protection
- Safe memory allocation within detected limits
- Prevents system crashes from memory overload
- Dynamic adjustment based on available resources

## Customization

### Modifying Chunk Sizes
Edit the `calculate_optimal_chunk` function in kernel.asm to adjust chunk size calculations.

### Changing Display
Modify main.asm to customize the userland interface and display elements.

### Adding Hardware Support
Extend the detection functions in kernel.asm to support additional hardware types.

## Troubleshooting

### Build Issues
- Ensure NASM is installed and in PATH
- Check file permissions on Linux/macOS
- Verify all source files are present

### Runtime Issues
- Increase VM memory allocation
- Check BIOS/UEFI boot settings
- Verify disk image integrity

### Performance Issues
- The system automatically optimizes for your hardware
- Monitor the real-time display for system status
- Check that adequate RAM is allocated

## Technical Details

### File Structure
- `boot.asm`: 512-byte bootloader with error handling
- `kernel.asm`: Main fractal loading engine with hardware detection
- `main.asm`: Isolated userland with dynamic display
- `Makefile`: Build configuration for the system

### Memory Usage
- Bootloader: 512 bytes
- Kernel: ~11KB (expandable)
- Userland: ~1.3KB (isolated)
- Dynamic data: Scales with detected hardware

### Performance Characteristics
- Loading speed scales with hardware capabilities
- Automatic optimization for detected storage type
- Thermal management prevents overheating
- Memory usage adapts to available RAM

## Recent Improvements

### Hardware Detection Enhancements
- Fixed RAM type detection and display
- Added RAM speed detection and display
- Enhanced disk type classification
- Added total storage calculation in MB

### Dynamic Calculations
- Implemented real-time cycle calculation
- Added dynamic chunk size optimization
- Enhanced progress tracking
- Real-time display updates

### User Interface Improvements
- Enhanced userland display with more information
- Added 32-bit number display support
- Improved error handling and isolation
- Better real-time data synchronization

## License
This project is provided as-is for educational and research purposes.

## Contributing
Feel free to submit improvements, bug fixes, or additional hardware support.

---
**Fractal OS** - Where hardware meets intelligent software adaptation.