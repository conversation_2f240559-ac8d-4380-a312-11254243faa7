#!/usr/bin/env python3
"""
Test script to verify the RAM detection fix works correctly.
This script will run QEMU with different memory sizes and check if the OS displays the correct values.
"""

import subprocess
import time
import os
import sys

def test_ram_detection(memory_mb, expected_kb):
    """Test RAM detection with a specific memory size."""
    print(f"Testing with {memory_mb}MB RAM (expecting {expected_kb} KB)...")
    
    # Run QEMU with the specified memory size
    cmd = [
        "qemu-system-i386",
        "-hda", "os.img",
        "-m", str(memory_mb),
        "-nographic",
        "-serial", "stdio",
        "-no-reboot"
    ]
    
    try:
        # Start QEMU process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=10
        )
        
        # Wait for output
        stdout, stderr = process.communicate(timeout=10)
        
        # Check if the expected value appears in the output
        if str(expected_kb) in stdout:
            print(f"✅ SUCCESS: Found {expected_kb} KB in output")
            return True
        elif "4294948864" in stdout:
            print(f"❌ FAILED: Still showing the old buggy value 4294948864 KB")
            return False
        else:
            print(f"⚠️  UNCLEAR: Expected {expected_kb} KB not found in output")
            print(f"Output: {stdout[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⚠️  TIMEOUT: QEMU didn't respond within 10 seconds")
        process.kill()
        return False
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Main test function."""
    print("RAM Detection Fix Verification Test")
    print("=" * 40)
    
    # Check if os.img exists
    if not os.path.exists("os.img"):
        print("❌ ERROR: os.img not found. Run 'make' first.")
        sys.exit(1)
    
    # Test cases: (memory_mb, expected_kb)
    test_cases = [
        (512, 524288),    # 512MB = 524288 KB
        (1024, 1048576),  # 1GB = 1048576 KB
        (2048, 2097152),  # 2GB = 2097152 KB
    ]
    
    results = []
    for memory_mb, expected_kb in test_cases:
        success = test_ram_detection(memory_mb, expected_kb)
        results.append(success)
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n" + "=" * 40)
    print("TEST SUMMARY:")
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 ALL TESTS PASSED ({passed}/{total})")
        print("The RAM detection fix is working correctly!")
    else:
        print(f"❌ SOME TESTS FAILED ({passed}/{total})")
        print("The RAM detection still needs work.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
