@echo off
echo ========================================
echo BYPASS MULTIPLICATION TEST
echo ========================================
echo.
echo BREAKTHROUGH DISCOVERY: The issue might be in the userland
echo multiplication: ram_total_kb * ram_speed_mhz
echo.
echo This test bypasses that multiplication and forces a safe
echo value (12345678) to see if that's where the 4294948864
echo overflow is coming from.
echo.

echo Testing with multiplication bypass...
timeout /t 2 > nul
start /wait qemu-system-i386 -hda os.img -m 16384 -display none -serial stdio -no-reboot
echo.

echo ========================================
echo MULTIPLICATION BYPASS ANALYSIS
echo ========================================
echo.
echo CRITICAL QUESTIONS:
echo.
echo 1. Did you see RAM: 16777216 KB (or similar reasonable value)? (Y/N)
set /p ram_reasonable=
echo.
echo 2. Did you see RAM Power: 12345678 (the forced value)? (Y/N)
set /p power_forced=
echo.
echo 3. Was there ANY sign of 4294948864 KB anywhere? (Y/N)
set /p overflow_present=
echo.

echo ========================================
echo BREAKTHROUGH ANALYSIS
echo ========================================
echo.
if "%ram_reasonable%"=="Y" if "%power_forced%"=="Y" if "%overflow_present%"=="N" (
    echo 🎉 BREAKTHROUGH: MULTIPLICATION WAS THE PROBLEM!
    echo.
    echo ✅ RAM VALUE REASONABLE: Shows correct RAM amount
    echo ✅ POWER VALUE FORCED: Shows 12345678 as expected
    echo ✅ NO OVERFLOW: No 4294948864 KB detected anywhere
    echo.
    echo PROBLEM IDENTIFIED:
    echo The overflow was happening in the userland multiplication:
    echo ram_total_kb * ram_speed_mhz = overflow
    echo.
    echo LIKELY SCENARIO:
    echo • Kernel detects RAM correctly (e.g., 16777216 KB)
    echo • Userland multiplies: 16777216 * 2400 = 40,265,318,400
    echo • This exceeds 32-bit limit (4,294,967,295)
    echo • Result wraps around to 4294948864 (0xFFFFFC00)
    echo • This corrupted value gets displayed as RAM amount
    echo.
    echo SOLUTION:
    echo Fix the multiplication to use safe arithmetic or
    echo display the values separately without multiplication.
    echo.
    echo Your kernel RAM detection was working correctly all along!
    echo The issue was in the userland display calculation!
    
) else if "%ram_reasonable%"=="N" if "%overflow_present%"=="Y" (
    echo ❌ MULTIPLICATION NOT THE ISSUE
    echo.
    echo The 4294948864 KB value still appears even with
    echo multiplication bypassed. This means the overflow
    echo is happening somewhere else:
    echo.
    echo • In the kernel detection (despite our fixes)
    echo • In the data transfer from kernel to userland
    echo • In a different calculation we haven't found
    echo • From a cached or stored value
    echo.
    echo Need to continue debugging other components.
    
) else (
    echo ⚠️  MIXED RESULTS
    echo.
    if "%ram_reasonable%"=="Y" (
        echo ✅ RAM value appears reasonable
    ) else (
        echo ❌ RAM value still problematic
    )
    echo.
    if "%power_forced%"=="Y" (
        echo ✅ Power value successfully forced
    ) else (
        echo ❌ Power value not forced correctly
    )
    echo.
    if "%overflow_present%"=="N" (
        echo ✅ No overflow detected
    ) else (
        echo ❌ Overflow still present
    )
    echo.
    echo This suggests the issue is complex and may involve
    echo multiple components or timing issues.
)
echo.
echo This test helps isolate whether the overflow is in:
echo • Kernel detection (if RAM still shows 4294948864)
echo • Userland multiplication (if RAM shows correct value)
echo • Other userland calculations (if mixed results)
echo.
pause
