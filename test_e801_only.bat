@echo off
echo ========================================
echo E801-ONLY RAM DETECTION TEST
echo ========================================
echo.
echo This test will help isolate if the issue is in E820 detection
echo by forcing the system to use E801 detection instead.
echo.
echo Testing with 16GB RAM using E801 detection only...
echo Expected: Should show a value close to 16GB but may be capped at 4GB
echo due to E801 limitations.
echo.
echo If this shows correct values, the problem is in E820 detection.
echo If this still shows 4294948864 KB, the problem is elsewhere.
echo.
timeout /t 3 > nul
start /wait qemu-system-i386 -hda os.img -m 16384 -display none -serial stdio -no-reboot
echo.
echo ========================================
echo E801 TEST RESULTS
echo ========================================
echo.
echo ANALYSIS:
echo - If RAM shows ~4194304 KB (4GB): E801 working, problem is in E820
echo - If RAM shows 4294948864 KB: Problem is NOT in E820, issue elsewhere
echo - If RAM shows realistic 16GB value: E801 somehow working better than E820
echo.
pause
