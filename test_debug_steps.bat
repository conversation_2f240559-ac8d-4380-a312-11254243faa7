@echo off
echo ========================================
echo STEP-BY-STEP DEBUG TEST
echo ========================================
echo.
echo This test will show exactly where the 0xFFFFFC00 overflow occurs
echo by tracing each step of the RAM detection process.
echo.

echo Testing with 16GB RAM (your configuration)
echo Expected to see step-by-step debug output showing where overflow happens
echo.
timeout /t 2 > nul
start /wait qemu-system-i386 -hda os.img -m 16384 -display none -serial stdio -no-reboot
echo.

echo ========================================
echo DEBUG ANALYSIS
echo ========================================
echo.
echo Look for these patterns in the output:
echo.
echo STEP 1 - INIT: 0 KB                    (Should show 0)
echo STEP 2 - TRYING E801                   (Attempting E801 detection)
echo STEP 3 - E801 RESULT: [value] KB       (Shows E801 result)
echo.
echo If STEP 3 shows 4294948864 KB, the problem is in E801 detection
echo If STEP 3 shows 0 KB, E801 failed and it tries INT88
echo.
echo STEP 5 - TRYING INT88                  (If E801 failed)
echo STEP 6 - INT88 RESULT: [value] KB      (Shows INT88 result)
echo.
echo If STEP 6 shows 4294948864 KB, the problem is in INT88 detection
echo.
echo The debug output will pinpoint exactly which detection method
echo is producing the problematic 0xFFFFFC00 overflow value.
echo.
pause
