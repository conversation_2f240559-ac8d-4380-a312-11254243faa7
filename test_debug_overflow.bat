@echo off
echo ========================================
echo DEBUG OVERFLOW - FIND EXACT SOURCE
echo ========================================
echo.
echo This version has detailed debugging to show exactly
echo where the 4294948864 KB (0xFFFFFC00) value is coming from.
echo.
echo It will show:
echo • Raw BIOS CX/DX values from E801
echo • DX value in hexadecimal
echo • Calculated value from DX conversion
echo • Values before and after addition
echo • Exact point where overflow occurs
echo.

echo Testing with 16GB configuration to debug overflow...
timeout /t 2 > nul
start /wait qemu-system-i386 -hda os.img -m 16384 -display none -serial stdio -no-reboot
echo.

echo ========================================
echo DEBUG ANALYSIS
echo ========================================
echo.
echo Please look at the debug output and answer:
echo.
echo 1. What was the BIOS DX value (in decimal)? 
set /p dx_decimal=
echo.
echo 2. What was the BIOS DX value (in hex)?
set /p dx_hex=
echo.
echo 3. What was the calculated value from DX?
set /p dx_calculated=
echo.
echo 4. What were the values before addition (A + B)?
set /p before_addition=
echo.
echo 5. What was the result after addition?
set /p after_addition=
echo.
echo 6. Did you see "OVERFLOW IN ADDITION" message? (Y/N)
set /p overflow_in_addition=
echo.

echo ========================================
echo OVERFLOW SOURCE ANALYSIS
echo ========================================
echo.
echo Based on your answers:
echo.
echo DX decimal: %dx_decimal%
echo DX hex: %dx_hex%
echo DX calculated: %dx_calculated%
echo Before addition: %before_addition%
echo After addition: %after_addition%
echo Overflow in addition: %overflow_in_addition%
echo.

if "%dx_calculated%"=="4294948864" (
    echo 🚨 OVERFLOW SOURCE: DX CONVERSION
    echo.
    echo The overflow happens when converting DX to KB.
    echo DX value %dx_decimal% (0x%dx_hex%) is causing overflow
    echo in the bit shift operation: DX << 6 (multiply by 64)
    echo.
    echo SOLUTION: Need safer DX conversion method
    echo • Use division instead of multiplication
    echo • Add bounds checking before bit shift
    echo • Implement range-based estimation
    
) else if "%after_addition%"=="4294948864" (
    echo 🚨 OVERFLOW SOURCE: ADDITION OPERATION
    echo.
    echo The overflow happens when adding DX memory to total.
    echo DX calculated: %dx_calculated% KB
    echo Addition: %before_addition% = %after_addition%
    echo.
    echo SOLUTION: Need safer addition method
    echo • Check for carry flag
    echo • Validate result before storing
    echo • Use 64-bit arithmetic if needed
    
) else if "%overflow_in_addition%"=="Y" (
    echo 🚨 OVERFLOW SOURCE: DETECTED IN ADDITION
    echo.
    echo The overflow was detected and prevented in addition.
    echo This means our safety checks are working but
    echo we need a better fallback method.
    
) else (
    echo ❓ OVERFLOW SOURCE: UNKNOWN
    echo.
    echo The overflow is not happening in the expected places.
    echo It might be occurring:
    echo • In a different function (INT88, fallback)
    echo • In the userland data transfer
    echo • In the display logic
    echo • From a cached/stored value
)
echo.
echo This debug information will help identify the exact
echo arithmetic operation causing the 0xFFFFFC00 overflow
echo so we can implement a targeted fix.
echo.
pause
