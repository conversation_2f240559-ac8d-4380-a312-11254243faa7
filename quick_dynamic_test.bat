@echo off
echo ========================================
echo QUICK DYNAMIC DETECTION TEST
echo ========================================
echo.
echo Testing dynamic RAM detection with 2 different memory sizes
echo to verify the system is not using hardcoded values.
echo.

echo Test 1: 4GB RAM System
echo Expected: ~4,194,304 KB, 1600 MHz DDR3
echo ----------------------------------------
timeout /t 2 > nul
start /wait qemu-system-i386 -hda os.img -m 4096 -display none -serial stdio -no-reboot
echo.

echo Test 2: 16GB RAM System  
echo Expected: ~16,777,216 KB, 2400 MHz DDR4
echo ----------------------------------------
timeout /t 2 > nul
start /wait qemu-system-i386 -hda os.img -m 16384 -display none -serial stdio -no-reboot
echo.

echo ========================================
echo QUICK VERIFICATION
echo ========================================
echo.
echo Did the two tests show DIFFERENT RAM amounts?
echo - Test 1 should show ~4GB (4,194,304 KB)
echo - Test 2 should show ~16GB (16,777,216 KB)
echo.
echo If both tests showed the same value, dynamic detection is not working.
echo If they showed different, proportional values, dynamic detection is working!
echo.
pause
