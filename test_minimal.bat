@echo off
echo ========================================
echo MINIMAL TEST - CHECK BASIC FUNCTIONS
echo ========================================
echo.
echo This test checks if the issue is in basic number printing
echo or somewhere else in the system.
echo.

echo Running minimal test...
timeout /t 2 > nul
start /wait qemu-system-i386 -hda os.img -m 16384 -display none -serial stdio -no-reboot
echo.

echo ========================================
echo MINIMAL TEST ANALYSIS
echo ========================================
echo.
echo Expected output:
echo TEST 1 - Print 123: 123
echo TEST 2 - Print 16777216: 16777216
echo TEST 3 - Print 4294948864: 4294948864
echo TEST 4 - Print 0xFFFFFC00: 4294948864
echo TEST 5 - ram_total_kb: 16777216
echo.
echo If TEST 3 and TEST 4 show the correct value (4294948864),
echo then the print function works correctly and the issue
echo is NOT in the basic number printing.
echo.
echo If TEST 5 shows 4294948864 instead of 16777216,
echo then something is corrupting the ram_total_kb variable
echo even when we force it to a known value.
echo.
echo This will help isolate whether the issue is:
echo 1. In the print function itself
echo 2. In memory corruption
echo 3. In the detection functions
echo 4. Somewhere else entirely
echo.
pause
