c[?7l[2J[0mSeaBI<PERSON> (version rel-1.16.3-0-ga6ed6b701f0a-prebuilt.qemu.org)


iPXE (http://ipxe.org) 00:03.0 CA00 PCI2.10 PnP PMM+1EFD1090+1EF31090 CA00
Press Ctrl-B to configure iPXE (PCI 00:03.0)...
                                                                               


Booting from Hard Disk...
<PERSON>ot failed: not a bootable disk

Booting from Floppy...
<PERSON><PERSON> failed: could not read the boot disk

Booting from DVD/CD...
<PERSON><PERSON> failed: Could not read from CDROM (code 0003)
Booting from ROM...
iPXE (PCI 00:03.0) starting execution...ok
iPXE initialising devices...ok



[0;1miPXE 1.20.1+ (g4bd0)[0m -- Open Source Network Boot Firmware -- [0;36mhttp://ipxe.org
[0mFeatures: DNS HTTP iSCSI TFTP AoE ELF MBOOT PXE bzImage Menu PXEXT

Press Ctrl-B for the iPXE command line...                                         net0: 52:54:00:12:34:56 using 82540em on 0000:00:03.0 (open)
  [Link:up, TX:0 TXE:0 RX:0 RXE:0]
Configuring (net0 52:54:00:12:34:56)...... ok
net0: *********/************* gw ********
Nothing to boot: No such file or directory (http://ipxe.org/2d03e13b)
No more network devices

Press Ctrl-B for the iPXE command line...                                         No bootable device.
