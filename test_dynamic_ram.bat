@echo off
echo ========================================
echo DYNAMIC RAM DETECTION TEST
echo ========================================
echo.

echo Testing dynamic RAM detection with different memory sizes...
echo This will verify that the OS detects actual RAM instead of hardcoded values.
echo.

echo Test 1: 1GB RAM (should show ~1048576 KB)
echo -----------------------------------------
timeout /t 2 > nul
start /wait qemu-system-i386 -hda os.img -m 1024 -serial stdio -display none -no-reboot
echo.

echo Test 2: 2GB RAM (should show ~2097152 KB)  
echo -----------------------------------------
timeout /t 2 > nul
start /wait qemu-system-i386 -hda os.img -m 2048 -serial stdio -display none -no-reboot
echo.

echo Test 3: 4GB RAM (should show ~4194304 KB)
echo -----------------------------------------
timeout /t 2 > nul
start /wait qemu-system-i386 -hda os.img -m 4096 -serial stdio -display none -no-reboot
echo.

echo Test 4: 8GB RAM (should show ~8388608 KB)
echo -----------------------------------------
timeout /t 2 > nul
start /wait qemu-system-i386 -hda os.img -m 8192 -serial stdio -display none -no-reboot
echo.

echo Test 5: 16GB RAM (should show ~16777216 KB)
echo ------------------------------------------
timeout /t 2 > nul
start /wait qemu-system-i386 -hda os.img -m 16384 -serial stdio -display none -no-reboot
echo.

echo ========================================
echo DYNAMIC DETECTION TEST COMPLETE
echo ========================================
echo.
echo Expected Results:
echo - 1GB:  ~1048576 KB
echo - 2GB:  ~2097152 KB  
echo - 4GB:  ~4194304 KB
echo - 8GB:  ~8388608 KB
echo - 16GB: ~16777216 KB
echo.
echo If all tests show different values that scale with memory size,
echo the dynamic detection is working correctly!
echo.
echo If all tests show the same value (like 524288 KB or 4294948864 KB),
echo the detection is still using hardcoded or buggy values.
echo.
pause
