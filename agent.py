import subprocess
import time
import os
import sys
import tkinter as tk
from tkinter import scrolledtext, messagebox
import pytesseract
from PIL import ImageGrab, Image
import pygetwindow as gw

OLLAMA_MODEL = "llama3.2:latest"  # Change if needed
FILES = ["boot.asm", "kernel.asm", "main.asm", "Makefile"]
QEMU_WINDOW_TITLE = "QEMU"  # Adjust if your QEMU window title differs

def read_source_files():
    sources = {}
    for f in FILES:
        if os.path.exists(f):
            with open(f, "r", encoding="utf-8") as file:
                sources[f] = file.read()
        else:
            sources[f] = ""
    return sources

def save_source_files(updated_sources):
    for f, content in updated_sources.items():
        with open(f, "w", encoding="utf-8") as file:
            file.write(content)

def create_prompt(instructions, sources):
    prompt = f"Instructions:\n{instructions}\n\nCurrent OS Source Files:\n"
    for fname, content in sources.items():
        prompt += f"\n<<<{fname}>>>\n{content}\n<<<end>>>\n"
    prompt += "\nUpdate these source files accordingly and return the full updated content of each file " \
              "in the same format with <<<filename>>> and <<<end>>> delimiters."
    return prompt

def query_ollama(model_name, prompt_text):
    proc = subprocess.Popen(
        ["ollama", "run", model_name],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    stdout, stderr = proc.communicate(prompt_text)
    if proc.returncode != 0:
        print("Ollama error:", stderr)
        return None
    return stdout

def parse_ollama_response(response_text):
    updated_files = {}
    lines = response_text.splitlines()
    current_file = None
    buffer = []
    for line in lines:
        if line.startswith("<<<") and line.endswith(">>>"):
            if current_file and buffer:
                updated_files[current_file] = "\n".join(buffer).strip()
                buffer = []
            # Extract filename between <<< and >>>
            current_file = line[3:-3].strip()
            if current_file == "end":
                current_file = None
        elif current_file:
            buffer.append(line)
    if current_file and buffer:
        updated_files[current_file] = "\n".join(buffer).strip()
    return updated_files

def build_os():
    result = subprocess.run(["make"], capture_output=True, text=True)
    print(result.stdout)
    if result.returncode != 0:
        print("Build error:", result.stderr)
        return False
    return True

def run_qemu():
    # Adjust if your OS image or qemu options differ
    qemu_command = [
        "qemu-system-x86_64",
        "-drive", "file=os.img,format=raw",
        "-m", "512M",
        "-nographic"
    ]
    return subprocess.Popen(qemu_command)

def find_qemu_window():
    windows = gw.getWindowsWithTitle(QEMU_WINDOW_TITLE)
    return windows[0] if windows else None

def take_screenshot_of_qemu():
    window = find_qemu_window()
    if not window:
        print("QEMU window not found.")
        return None
    window.activate()
    time.sleep(0.5)
    bbox = (window.left, window.top, window.right, window.bottom)
    img = ImageGrab.grab(bbox)
    img.save("qemu_screenshot.png")
    return img

def ocr_image(image):
    return pytesseract.image_to_string(image).strip()

def beep():
    if sys.platform == "win32":
        import winsound
        winsound.MessageBeep()
    else:
        print("\a")

# === GUI ===

class OSAgentApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("OS Dev AI Agent with Ollama")
        self.geometry("800x600")

        self.instructions_label = tk.Label(self, text="Enter instructions for your OS:")
        self.instructions_label.pack(pady=5)

        self.instructions_text = scrolledtext.ScrolledText(self, height=10)
        self.instructions_text.pack(fill=tk.BOTH, expand=False, padx=10, pady=5)

        self.run_button = tk.Button(self, text="Run Agent Loop", command=self.run_agent_loop)
        self.run_button.pack(pady=10)

        self.log_text = scrolledtext.ScrolledText(self, height=20)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        self.log_text.config(state=tk.DISABLED)

        self.protocol("WM_DELETE_WINDOW", self.on_close)
        self.qemu_process = None

    def log(self, message):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        print(message)

    def on_close(self):
        if self.qemu_process:
            self.qemu_process.terminate()
        self.destroy()

    def run_agent_loop(self):
        instructions = self.instructions_text.get("1.0", tk.END).strip()
        if not instructions:
            messagebox.showerror("Error", "Please enter instructions before running.")
            return

        self.log("Reading current source files...")
        sources = read_source_files()

        iteration = 1
        while True:
            self.log(f"\n=== Iteration {iteration} ===")

            prompt = create_prompt(instructions, sources)
            self.log("Sending prompt to Ollama...")
            response = query_ollama(OLLAMA_MODEL, prompt)
            if response is None:
                self.log("Ollama returned an error, stopping.")
                return

            self.log("Parsing Ollama response...")
            updated_sources = parse_ollama_response(response)
            if not updated_sources:
                self.log("No files parsed from Ollama response, stopping.")
                return

            save_source_files(updated_sources)
            sources = updated_sources

            self.log("Building OS with make...")
            if not build_os():
                self.log("Build failed, retrying iteration...")
                iteration += 1
                continue

            self.log("Running QEMU...")
            if self.qemu_process:
                self.qemu_process.terminate()
                time.sleep(1)
            self.qemu_process = run_qemu()

            self.log("Waiting for QEMU to boot...")
            time.sleep(5)  # Adjust as needed for boot time

            self.log("Taking screenshot of QEMU window...")
            screenshot = take_screenshot_of_qemu()
            if screenshot is None:
                self.log("Failed to get screenshot, stopping.")
                return

            self.log("Performing OCR on screenshot...")
            ocr_text = ocr_image(screenshot)
            self.log(f"OCR output:\n{ocr_text}")

            self.log("Comparing OCR output to instructions...")
            # Simple comparison: check if all instruction words are in OCR text
            instruction_words = set(instructions.lower().split())
            ocr_words = set(ocr_text.lower().split())

            if instruction_words.issubset(ocr_words):
                self.log("Output matches instructions! Beeping and stopping.")
                beep()
                break
            else:
                self.log("Output does not match instructions. Looping for next iteration...")
                iteration += 1
                # Optional: add max iteration limit here

        if self.qemu_process:
            self.qemu_process.terminate()

if __name__ == "__main__":
    app = OSAgentApp()
    app.mainloop()
