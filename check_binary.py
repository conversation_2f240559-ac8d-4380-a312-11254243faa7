#!/usr/bin/env python3
"""
Check the compiled binary for the problematic RAM value.
"""

import struct
import sys

def check_for_problematic_value(filename):
    """Check if the problematic value 4294948864 (0xFFFFFC00) exists in the binary."""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        # Convert the problematic value to bytes (little-endian 32-bit)
        problematic_value = 4294948864
        problematic_bytes = struct.pack('<I', problematic_value)
        
        # Also check for the hex representation
        hex_bytes = bytes.fromhex('00FCFFFF')  # 0xFFFFFC00 in little-endian
        
        print(f"Checking {filename} for problematic values...")
        print(f"Looking for: {problematic_value} (0x{problematic_value:08X})")
        
        # Search for the problematic value
        found_decimal = data.find(problematic_bytes)
        found_hex = data.find(hex_bytes)
        
        if found_decimal >= 0:
            print(f"❌ FOUND problematic value at offset 0x{found_decimal:04X}")
            return False
        elif found_hex >= 0:
            print(f"❌ FOUND problematic hex value at offset 0x{found_hex:04X}")
            return False
        else:
            print(f"✅ No problematic values found in {filename}")
            
        # Also check for the correct value (524288 = 512MB in KB)
        correct_value = 524288
        correct_bytes = struct.pack('<I', correct_value)
        found_correct = data.find(correct_bytes)
        
        if found_correct >= 0:
            print(f"✅ Found correct value {correct_value} at offset 0x{found_correct:04X}")
        else:
            print(f"⚠️  Correct value {correct_value} not found (might be set at runtime)")
            
        return True
        
    except FileNotFoundError:
        print(f"❌ File {filename} not found")
        return False
    except Exception as e:
        print(f"❌ Error reading {filename}: {e}")
        return False

def main():
    """Main function."""
    print("Binary Analysis for RAM Detection Fix")
    print("=" * 40)
    
    files_to_check = ['kernel.bin', 'userland.bin', 'boot.bin']
    
    all_clean = True
    for filename in files_to_check:
        clean = check_for_problematic_value(filename)
        all_clean = all_clean and clean
        print()
    
    if all_clean:
        print("🎉 All binaries are clean of problematic values!")
    else:
        print("❌ Some binaries still contain problematic values!")
    
    return all_clean

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
